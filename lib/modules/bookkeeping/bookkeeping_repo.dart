import 'package:qiazhun/common/base_model.dart';
import 'package:qiazhun/common/http.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';

class BookkeepingRepo {
  static Future<BaseModel<dynamic>> addBookkeeping(String name, bool isJoinTotal, String memo, String icon, String? bookkeepingNumber) async {
    var data = {'accountBookName': name, 'isJoinTotal': isJoinTotal ? '1' : '2', 'memo': memo, 'icon': icon};
    if (bookkeepingNumber?.isNotEmpty == true) {
      data['bookkeepingNumber'] = bookkeepingNumber!;
    }
    var resp = await HttpUtil().post('api/bookkeeping/addBookkeeping', data: data);
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> deleteBill(String moneyLogId) async {
    var resp = await HttpUtil().post('api/bookkeeping/deleteBill', data: {'moneylogId': moneyLogId});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<List<BookkeepingInfo>>> getBookkeepingList() async {
    var resp = await HttpUtil().post('api/bookkeeping/getBookkeepingList');
    return BaseModel.fromJson(resp, (json) => (json as List<dynamic>).map((dynamic e) => BookkeepingInfo.fromJson(e)).toList());
  }

  static Future<BaseModel<BookkeepingInfo>> getBookkeepingDetail(String bookkeepingNumber) async {
    var resp = await HttpUtil().post('api/bookkeeping/bookkeepingDetail', data: {'bookkeepingNumber': bookkeepingNumber});
    return BaseModel.fromJson(resp, (json) => BookkeepingInfo.fromJson(json));
  }

  static Future<BaseModel<dynamic>> delBookkeeping(String bookkeepingNumber) async {
    var resp = await HttpUtil().post('api/bookkeeping/delBookkeeping', data: {'bookkeepingNumber': bookkeepingNumber});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> sortBookkeeping(String sortedId) async {
    var resp = await HttpUtil().post('api/bookkeeping/updateBookkeepingSort', data: {'sortedId': sortedId});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> delBookkeepingCategory(String bookkeepingCategoryId) async {
    var resp = await HttpUtil().post('api/bookkeeping/deleteBookkeepingCategory', data: {'bookkeepingCategoryId': bookkeepingCategoryId});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> sortBookkeepingCategory(String type, String idList) async {
    var resp = await HttpUtil().post('api/bookkeeping/updateBookkeepingCategorySort', data: {'categoryType': type, 'sortedId': idList});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<dynamic>> addBookkeepingCategory(num pid, String icon, String name, String categoryType) async {
    var resp = await HttpUtil().post('api/bookkeeping/addBookkeepingCategory',
        data: {'pid': pid.toString(), 'icon': icon, 'bookkeepingCategoryName': name, 'categoryType': categoryType});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<BookkeepingCategoryResp>> getBookkeepingCategory() async {
    var resp = await HttpUtil().post('api/bookkeeping/getBookkeepingCategory', data: {});
    return BaseModel.fromJson(resp, (json) => BookkeepingCategoryResp.fromJson(json));
  }

  static Future<BaseModel<dynamic>> addOfficialCategory(String bookkeepingCategoryId) async {
    var resp = await HttpUtil().post('api/bookkeeping/addOfficialCategory', data: {'bookkeepingCategoryId': bookkeepingCategoryId});
    return BaseModel.fromJson(resp, (json) => json);
  }

  static Future<BaseModel<List<CategoryItem>>> getBookkeepingCategoryPage(String categoryType) async {
    var resp = await HttpUtil().post('api/bookkeeping/getBookkeepingCategoryPage', data: {'categoryType': categoryType});
    return BaseModel.fromJson(resp, (json) => (json as List<dynamic>).map((dynamic e) => CategoryItem.fromJson(e)).toList());
  }

  static Future<BaseModel<List<AccountModel>>> getAccountList({required int? page, required int? pageCount}) async {
    var resp = await HttpUtil().post('api/card/list', data: {'page': page, 'pageCount': pageCount});
    return BaseModel.fromJson(resp, (json) => (json as List<dynamic>).map((dynamic e) => AccountModel.fromJson(e)).toList());
  }

  static Future<BaseModel<BillDetailInfo>> billDetail(String moneyLogId) async {
    var resp = await HttpUtil().post('api/bookkeeping/billDetail', data: {'moneyLogId': moneyLogId});
    return BaseModel.fromJson(resp, (json) => BillDetailInfo.fromJson(json));
  }

  static Future<BaseModel<FinancialDataResp>> getFinancialData() async {
    var resp = await HttpUtil().post(
      'api/bookkeeping/getFinancialData',
    );
    return BaseModel.fromJson(resp, (json) => FinancialDataResp.fromJson(json));
  }

  static Future<BaseModel<dynamic>> addBill(
      {required String bookkeepingNumber,
      required int categoryId,
      required String action,
      required String money,
      required String isSave,
      String? isSaveMoney,
      required String isNecessaryStatus,
      required String nowTime,
      required int accountId,
      String? memo,
      String? moneyLogId}) async {
    var data = {
      'bookkeepingNumber': bookkeepingNumber,
      'categoryId': '$categoryId',
      'memo': memo,
      'action': action,
      'money': money,
      'isSave': isSave,
      'isNecessaryStatus': isNecessaryStatus,
      'nowTime': nowTime,
      'accountId': accountId
    };
    if (isSaveMoney?.isNotEmpty == true) {
      data['isSaveMoney'] = isSaveMoney!;
    }
    if (moneyLogId?.isNotEmpty == true) {
      data['moneyLogId'] = moneyLogId;
    }
    var resp = await HttpUtil().post('api/bookkeeping/addBill', data: data);
    return BaseModel.fromJson(resp, (json) => json);
  }

  // 获取账本详情和流水记录
  static Future<BaseModel<BookkeepingDetailResp>> getBookkeepingDetailWithLogs(
      {required String bookkeepingNumber,
      required String timeInterval, // 时间区间
      required String type}) async {
    var resp = await HttpUtil()
        .post('api/bookkeeping/bookkeepingMoneyLogDetail', data: {"timeInterval": timeInterval, "bookkeepingNumber": bookkeepingNumber, 'type': type});
    return BaseModel.fromJson(resp, (json) => BookkeepingDetailResp.fromJson(json));
  }

  // 设置快捷账户
  static Future<BaseModel<dynamic>> setShortcutAccount({required int type, required String accountId}) async {
    var resp = await HttpUtil().post('api/card/setShortcutAccount', data: {'type': type, 'accountId': accountId});
    return BaseModel.fromJson(resp, (json) => json);
  }

  // 取消快捷账户
  static Future<BaseModel<dynamic>> cancelShortcutAccount({required int type, required String accountId}) async {
    var resp = await HttpUtil().post('api/card/cancelShortcutAccount', data: {'type': type, 'accountId': accountId});
    return BaseModel.fromJson(resp, (json) => json);
  }

  // 排序快捷账户
  static Future<BaseModel<dynamic>> sortShortcutAccounts({required int type, required List<String> accounts}) async {
    var resp = await HttpUtil().post('api/card/sortShortcutAccounts', data: {'type': type, 'accounts': accounts});
    return BaseModel.fromJson(resp, (json) => json);
  }

  // 获取快捷账户
  static Future<BaseModel<ShortcutAccountsResp>> getShortcutAccounts() async {
    var resp = await HttpUtil().post('api/card/getShortcutAccounts');
    return BaseModel.fromJson(resp, (json) => ShortcutAccountsResp.fromJson(json));
  }

  // 获取记账页面上下文信息
  static Future<BaseModel<BookkeepingContextResp>> getBookkeepingContext({String? accountId, String? moneyLogId}) async {
    var data = {};
    if (accountId?.isNotEmpty == true) {
      data['accountId'] = accountId;
    }
    if (moneyLogId?.isNotEmpty == true) {
      data['moneyLogId'] = moneyLogId;
    }
    var resp = await HttpUtil().post('api/bookkeeping/getBookkeepingContext', data: data);
    return BaseModel.fromJson(resp, (json) => BookkeepingContextResp.fromJson(json));
  }

  static Future<BaseModel<List<AccountModel>>> getDefaultAccount(int categoryId) async {
    var data = {"categoryId": categoryId};
    var resp = await HttpUtil().post('api/bookkeeping/getDefaultAccount', data: data);
    return BaseModel.fromJson(resp, (json) => (json as List<dynamic>).map((dynamic e) => AccountModel.fromJson(e)).toList());
  }
}
