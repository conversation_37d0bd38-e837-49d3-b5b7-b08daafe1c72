import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_page/bookkeeping_controller.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';
import 'package:qiazhun/widgets/round_image.dart';

class BookkeepingCategoryView extends GetView<BookkeepingController> {
  const BookkeepingCategoryView({
    super.key,
  });

  List<List<Widget>> _generatePages(List<CategoryItem> categories) {
    List<List<Widget>> pages = [];
    List<Widget> pageItems = [];

    bool settingAdded = false;
    // 添加分类按钮
    for (int i = 0; i < categories.length; i++) {
      pageItems.add(_itemView(i, categories[i]));

      // 每10个分类作为一页
      if (pageItems.length == 10 || i == categories.length - 1) {
        // 如果是最后一页，添加设置按钮
        if (i == categories.length - 1) {
          if (pageItems.length < 10) {
            pageItems.add(_settingItemView());
            settingAdded = true;
          } else {
            pages.add([...pageItems]);
            pageItems = [_settingItemView()];
            settingAdded = true;
          }
        }
        pages.add([...pageItems]);
        pageItems = [];
      }
    }

    // 如果最后一页没有设置按钮，添加一个新页面放置设置按钮
    if (!settingAdded) {
      pages.add([_settingItemView()]);
    }
    return pages;
  }

  @override
  Widget build(BuildContext context) {
    double space = (MediaQuery.of(context).size.width - 5 * 70 - 20) / 4;

    return SizedBox(
      height: 145,
      child: Column(
        children: [
          Expanded(
            child: Obx(() {
              List<List<Widget>> incomePages = _generatePages(controller.incomeCategories);
              List<List<Widget>> outcomePages = _generatePages(controller.outcomeCategories);
              final currentPageIndex = controller.selectedTab.value == 0 ? controller.currentOutcomePage.value : controller.currentIncomePage.value;
              final pages = controller.selectedTab.value == 0 ? outcomePages : incomePages;
              return Stack(
                children: [
                  PageView.builder(
                    controller: controller.pageController,
                    onPageChanged: (page) {
                      if (controller.selectedTab.value == 0) {
                        controller.currentOutcomePage.value = page;
                      } else {
                        controller.currentIncomePage.value = page;
                      }
                    },
                    itemCount: pages.length,
                    itemBuilder: (context, pageIndex) {
                      return Container(
                        margin: const EdgeInsets.symmetric(horizontal: 10),
                        child: Wrap(
                          spacing: space,
                          runSpacing: 4,
                          alignment: WrapAlignment.start,
                          children: pages[pageIndex].map((widget) {
                            return SizedBox(
                              width: 70,
                              child: widget,
                            );
                          }).toList(),
                        ),
                      );
                    },
                  ),
                  if (pages.length > 1)
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(
                          pages.length,
                          (index) => Container(
                            margin: const EdgeInsets.symmetric(horizontal: 4),
                            width: 6,
                            height: 6,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: currentPageIndex == index ? MColor.skin : MColor.xFFEBEBEB,
                            ),
                          ),
                        ),
                      ),
                    ),
                ],
              );
            }),
          ),
          const SizedBox(height: 7),
        ],
      ),
    );
  }

  Widget _settingItemView() {
    return GestureDetector(
      onTap: () {
        RouterHelper.router.pushNamed(Routes.settingCategoryPath, extra: {'tab_id': controller.selectedTab.value}).then((_) {
          // _loadData();
        });
      },
      child: Column(
        children: [
          Image.asset('assets/images/ic_setting.png', width: 38, height: 38),
          const SizedBox(height: 3),
          Text(
            '设置',
            style: TextStyle(height: 1.4, fontSize: 12, color: MColor.xFF1B1C1A),
          ),
          const SizedBox(height: 8)
        ],
      ),
    );
  }

  Widget _itemView(int index, CategoryItem item) {
    final selectedCatetory = controller.selectedTab.value == 0 ? controller.selectedOutcomeCategory : controller.selectedIncomeCategory;
    final _selected = selectedCatetory.value;
    return GestureDetector(
      onTap: () {
        if (_selected == item) {
          if (controller.selectedTab.value == 0) {
            controller.selectedOutcomeCategory.value = null;
          } else {
            controller.selectedIncomeCategory.value = null;
          }
          controller.onCategorySelected(null);
        } else {
          if (controller.selectedTab.value == 0) {
            controller.selectedOutcomeCategory.value = item;
          } else {
            controller.selectedIncomeCategory.value = item;
          }
          controller.onCategorySelected(item);
        }
      },
      child: SizedBox(
        width: 62,
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: _selected?.bookkeepingCategoryId == item.bookkeepingCategoryId ? MColor.skin : Colors.transparent,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(19),
                color: MColor.xFFECECEC,
              ),
              child: RoundImage(
                imageUrl: item.bookkeepingCategoryIcon ?? '',
                radius: 17,
                size: 34,
              ),
            ),
            const SizedBox(height: 3),
            FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                item.bookkeepingCategoryName ?? '',
                maxLines: 1,
                style: TextStyle(
                  height: 1.4,
                  fontSize: 12,
                  color: _selected?.bookkeepingCategoryId == item.bookkeepingCategoryId ? MColor.skin : MColor.xFF1B1C1A,
                ),
              ),
            ),
            const SizedBox(height: 8)
          ],
        ),
      ),
    );
  }
}
