import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_view.dart';
import 'package:math_expressions/math_expressions.dart';
import 'package:qiazhun/constants/design.dart';
import 'package:qiazhun/modules/account/account_model.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_page/bookkeeping_controller.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_repo.dart';
import 'package:qiazhun/modules/bookkeeping/bookkeeping_store.dart';
import 'package:qiazhun/router/router.dart';
import 'package:qiazhun/tools/tools.dart';

class BookkeepingKeyboardView extends GetView<BookkeepingController> {
  const BookkeepingKeyboardView({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: _buildKeyboardRows(context),
    );
  }

  List<Widget> _buildKeyboardRows(BuildContext context) {
    return [
      // 第一行：运算符号
      _buildKeyboardRow([
        _KeyboardItem(
          ratio: 1,
          icon: 'assets/images/ic_cal_div.png',
          iconWidth: 16,
          iconHeight: 16,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('÷'),
        ),
        _KeyboardItem(
          ratio: 1,
          icon: 'assets/images/ic_cal_mul.png',
          iconWidth: 16,
          iconHeight: 16,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('×'),
        ),
        _KeyboardItem(
          ratio: 1,
          icon: 'assets/images/ic_cal_sub.png',
          iconWidth: 16,
          iconHeight: 16,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('-'),
        ),
        _KeyboardItem(
          ratio: 1,
          icon: 'assets/images/ic_cal_add.png',
          iconWidth: 16,
          iconHeight: 16,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('+'),
        ),
        _KeyboardItem(
          ratio: 1,
          icon: 'assets/images/ic_cal_back.png',
          iconWidth: 27,
          iconHeight: 20,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 0,
          onTap: controller.deleteText,
        ),
      ], marginTop: 0),

      // 第二行：1 2 3 4 再记
      _buildKeyboardRow([
        _KeyboardItem(
          ratio: 1,
          text: '1',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('1'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '2',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('2'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '3',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('3'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '4',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('4'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '再记',
          fontSize: 14,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 0,
          onTap: () => controller.doRequest(needPop: false),
        ),
      ], marginTop: 10),

      // 第三行：5 6 7 8 账户
      _buildKeyboardRow([
        _KeyboardItem(
          ratio: 1,
          text: '5',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('5'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '6',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('6'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '7',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('7'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '8',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('8'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: controller.bookkeepingInfo.value?.accountBookName ?? '账本',
          fontSize: 16,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 0,
          onTap: controller.onBookkeepingTap,
        ),
      ], marginTop: 10),

      // 第四行：设置 9 . 0 确认
      _buildKeyboardRow([
        _KeyboardItem(
          ratio: 1,
          icon: 'assets/images/ic_setting.png',
          iconWidth: 20,
          iconHeight: 20,
          bgColor: MColor.xFFEEEEEE,
          borderColor: MColor.xFF999999,
          marginRight: 10,
          onTap: () {
            _showAccountSelectionDialog(context);
          },
        ),
        _KeyboardItem(
          ratio: 1,
          text: '9',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('9'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '.',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('.'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '0',
          fontSize: 24,
          bgColor: MColor.xFFFFFFFF,
          borderColor: MColor.skin,
          marginRight: 10,
          onTap: () => controller.appendText('0'),
        ),
        _KeyboardItem(
          ratio: 1,
          text: '确认',
          fontSize: 16,
          fontColor: MColor.xFFFFFFFF,
          bgColor: MColor.xFFFF918D,
          borderColor: MColor.xFFED726E,
          marginRight: 0,
          onTap: () => controller.doRequest(needPop: true),
        ),
      ], marginTop: 10),
    ];
  }

  Widget _buildKeyboardRow(List<_KeyboardItem> items, {required double marginTop}) {
    List<Widget> rowWidgets = [];

    for (var item in items) {
      rowWidgets.add(Expanded(
        flex: item.ratio,
        child: GestureDetector(
          onTap: item.onTap,
          child: Container(
            decoration: BoxDecoration(
              color: item.bgColor,
              borderRadius: BorderRadius.circular(10),
              border: Border.all(color: item.borderColor, width: 1),
              boxShadow: [
                BoxShadow(
                  offset: const Offset(0, 2),
                  blurRadius: 0,
                  color: item.borderColor,
                ),
              ],
            ),
            height: 36,
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: Center(
              child: item.widget ??
                  (item.icon != null
                      ? Image.asset(
                          item.icon!,
                          width: item.iconWidth,
                          height: item.iconHeight,
                          fit: BoxFit.fill,
                        )
                      : FittedBox(
                          fit: BoxFit.fill,
                          child: Text(
                            item.text!,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontSize: item.fontSize!,
                              height: 1,
                              color: item.fontColor ?? item.borderColor,
                            ),
                          ),
                        )),
            ),
          ),
        ),
      ));

      if (item.marginRight > 0) {
        rowWidgets.add(SizedBox(width: item.marginRight));
      }
    }

    return Container(
      margin: EdgeInsets.only(top: marginTop),
      child: Row(children: rowWidgets),
    );
  }

  void _showAccountSelectionDialog(BuildContext context) {
    RouterHelper.router.pushNamed(
      Routes.customPopupPath,
      extra: {
        'title': '设置快捷账户',
        'widget': _AccountSelectionWidget(),
        'onConfirm': () async {
          // 确认操作
        },
      },
    ).then((_) {
      controller.isShortcutManageMode.value = false;
    });
  }
}

class _KeyboardItem {
  final int ratio;
  final String? icon;
  final double? iconWidth;
  final double? iconHeight;
  final Color bgColor;
  final Color borderColor;
  final double marginRight;
  final String? text;
  final double? fontSize;
  final Color? fontColor;
  final Widget? widget;
  final VoidCallback? onTap;

  _KeyboardItem({
    required this.ratio,
    this.icon,
    this.iconWidth,
    this.iconHeight,
    required this.bgColor,
    required this.borderColor,
    required this.marginRight,
    this.text,
    this.fontSize,
    this.fontColor,
    this.widget,
    this.onTap,
  });
}

class _AccountSelectionWidget extends GetView<BookkeepingController> {
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isShortcutManageMode.value) {
        // 管理模式：只显示快捷账户，可拖动排序
        return _buildManageMode();
      } else {
        // 普通模式：显示所有账户
        return _buildNormalMode();
      }
    });
  }

  Widget _buildManageMode() {
    List<AccountModel> shortcuts = controller.selectedTab.value == 0 ? controller.shortcutOutcomeAccounts : controller.shortcutIncomeAccounts;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(
          '快捷账户',
          trailingWidget: GestureDetector(
            onTap: () {
              // setState(() {
              controller.isShortcutManageMode.value = !controller.isShortcutManageMode.value;
              // });
            },
            child: Text(
              controller.isShortcutManageMode.value ? '完成' : '排序',
              style: const TextStyle(fontSize: 14, color: MColor.skin),
            ),
          ),
        ),
        const SizedBox(height: 12),
        ReorderableListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: shortcuts.length,
          onReorder: (oldIndex, newIndex) {
            if (newIndex > oldIndex) {
              newIndex -= 1;
            }
            final item = shortcuts.removeAt(oldIndex);
            shortcuts.insert(newIndex, item);
            controller.saveShortcutOrder(shortcuts);
          },
          itemBuilder: (context, index) {
            final account = shortcuts[index];
            return _buildAccountTile(
              account,
              key: ValueKey(account.id),
              showTrailing: true,
              trailingWidget: const Icon(Icons.drag_handle, color: MColor.xFF999999),
            );
          },
        ),
      ],
    );
  }

  Widget _buildNormalMode() {
    return Obx(() {
      List<Widget> children = [];

      List<AccountModel> shortcuts = controller.selectedTab.value == 0 ? controller.shortcutOutcomeAccounts : controller.shortcutIncomeAccounts;

      // 快捷账户
      if (shortcuts.isNotEmpty) {
        // 其他账户
        children.add(_buildSectionTitle(
          '快捷账户',
          trailingWidget: GestureDetector(
            onTap: () {
              // setState(() {
              controller.isShortcutManageMode.value = !controller.isShortcutManageMode.value;
              // });
            },
            child: Text(
              controller.isShortcutManageMode.value ? '完成' : '排序',
              style: const TextStyle(fontSize: 14, color: MColor.skin),
            ),
          ),
        ));

        for (var account in shortcuts) {
          // if (widget.lastAccount == null || account.id != widget.lastAccount!.id) {
          children.add(_buildAccountTile(account, isShortcut: true));
          // }
        }
        children.add(const SizedBox(height: 8));

        // 分割线
        children.add(const Divider(color: MColor.xFFEEEEEE, height: 1));
        children.add(const SizedBox(height: 8));
      }

      final otherAccounts = BookkeepingStore.to.accountList.where((account) {
        final isShortcut = shortcuts.any((shortcut) => shortcut.id == account.id);
        return !isShortcut;
      }).toList();

      if (otherAccounts.isNotEmpty) {
        // 其他账户
        children.add(_buildSectionTitle('添加快捷'));

        for (var account in otherAccounts) {
          children.add(_buildAccountTile(account, isShortcut: false));
        }
      }

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      );
    });
  }

  Widget _buildSectionTitle(String title, {Widget? trailingWidget}) {
    return Container(
      child: ListTile(
          dense: true,
          visualDensity: VisualDensity.compact,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
          title: Text(
            title,
            style: TextStyle(fontSize: 14, color: MColor.xFF999999),
          ),
          trailing: trailingWidget),
    );
  }

  Widget _buildAccountTile(
    AccountModel account, {
    Key? key,
    bool isShortcut = false,
    bool showTrailing = false,
    Widget? trailingWidget,
  }) {
    return Container(
      key: key,
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
        leading: Image.asset('assets/images/ic_card.png', width: 24, height: 24),
        title: Text(
          account.accountName ?? '账户',
          style: const TextStyle(fontSize: 14, color: MColor.xFF1B1C1A),
        ),
        trailing: showTrailing
            ? trailingWidget
            : isShortcut
                ? GestureDetector(
                    onTap: () => controller.cancelShortcutAccount(account),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: MColor.xFFFF7858,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        '取消快捷',
                        style: TextStyle(fontSize: 12, color: MColor.xFFFFFFFF),
                      ),
                    ),
                  )
                : GestureDetector(
                    onTap: () => controller.setShortcutAccount(account),
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: MColor.skin,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        '添加快捷',
                        style: TextStyle(fontSize: 12, color: MColor.xFFFFFFFF),
                      ),
                    ),
                  ),
        onTap: () {
          // widget.onAccountSelected(account);
          RouterHelper.router.pop();
        },
      ),
    );
  }
}
